import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import { Prompt, Category, User } from '../../../models';
import { sendNewPromptNotification, emailService } from '../../../lib/emailService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();
  if (req.method === 'GET') {
    try {
      const {
        platform,
        category,
        search,
        sort = 'newest',
        verified,
        page = '1',
        limit = '9'
      } = req.query;

      const filter: any = {};

      // Platform filter
      if (platform && platform !== 'All Platforms') {
        filter.platform = platform;
      }

      // Category filter by name
      if (category && category !== 'All Categories') {
        const categoryDoc = await Category.findOne({ name: category });
        if (categoryDoc) {
          filter.category = categoryDoc._id;
        } else {
          // If category doesn't exist, return empty results
          return res.status(200).json({
            prompts: [],
            totalPages: 0,
            currentPage: parseInt(page as string),
            totalPrompts: 0
          });
        }
      }

      // Verified filter
      if (verified === 'true') {
        filter.verified = true;
      }

      // Search filter with minimum length validation
      if (search) {
        if ((search as string).length < 2) {
          return res.status(400).json({
            error: 'Search term too short',
            message: 'Please enter at least 2 characters to search'
          });
        }

        filter.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { tags: { $in: [new RegExp(search as string, 'i')] } },
        ];
      }

      // Sorting
      let sortOption: any = { createdAt: -1 }; // default: newest first
      switch (sort) {
        case 'oldest':
          sortOption = { createdAt: 1 };
          break;
        case 'title':
          sortOption = { title: 1 };
          break;
        case 'rating':
          // We'll sort by rating count in aggregation
          break;
        default:
          sortOption = { createdAt: -1 };
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      let prompts;
      let totalPrompts;

      if (sort === 'rating') {
        // Use aggregation for rating-based sorting
        const aggregationPipeline = [
          { $match: filter },
          {
            $addFields: {
              ratingScore: {
                $sum: {
                  $map: {
                    input: '$ratings',
                    as: 'rating',
                    in: '$$rating.value'
                  }
                }
              }
            }
          },
          { $sort: { ratingScore: -1, createdAt: -1 } },
          { $skip: skip },
          { $limit: limitNum },
          {
            $lookup: {
              from: 'categories',
              localField: 'category',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $lookup: {
              from: 'users',
              localField: 'created_by',
              foreignField: '_id',
              as: 'created_by'
            }
          },
          {
            $addFields: {
              category: { $arrayElemAt: ['$category', 0] },
              created_by: { $arrayElemAt: ['$created_by', 0] }
            }
          },
          {
            $project: {
              'created_by.password': 0,
              'created_by.activity_log': 0
            }
          }
        ];

        prompts = await Prompt.aggregate(aggregationPipeline as any);
        totalPrompts = await Prompt.countDocuments(filter);
      } else {
        // Regular query with populate
        prompts = await Prompt.find(filter)
          .populate('category')
          .populate('created_by', '-password -activity_log')
          .sort(sortOption)
          .skip(skip)
          .limit(limitNum);

        totalPrompts = await Prompt.countDocuments(filter);
      }

      const totalPages = Math.ceil(totalPrompts / limitNum);

      res.status(200).json({
        prompts,
        totalPages,
        currentPage: pageNum,
        totalPrompts
      });
    } catch (error) {
      console.error('Error fetching prompts:', error);

      // Return mock data for testing when database is unavailable
      const mockPrompts = [
        {
          _id: '1',
          title: 'Test Writing Prompt',
          description: 'A test prompt for writing assistance',
          prompt_text: 'Help me write a professional email',
          platform: 'ChatGPT',
          category: { name: 'Writing', _id: 'cat1' },
          tags: ['writing', 'email', 'professional'],
          verified: true,
          created_at: new Date(),
          user: { name: 'Test User' },
          rating: { average: 4.5, count: 10 }
        },
        {
          _id: '2',
          title: 'Code Review Helper',
          description: 'A prompt for code review assistance',
          prompt_text: 'Review this code for best practices',
          platform: 'GitHub Copilot',
          category: { name: 'Development', _id: 'cat2' },
          tags: ['code', 'review', 'development'],
          verified: true,
          created_at: new Date(),
          user: { name: 'Dev User' },
          rating: { average: 4.8, count: 15 }
        },
        {
          _id: '3',
          title: 'Image Generation Prompt',
          description: 'Create beautiful images with AI',
          prompt_text: 'Generate a sunset landscape image',
          platform: 'Midjourney',
          category: { name: 'Art', _id: 'cat3' },
          tags: ['image', 'art', 'landscape'],
          verified: false,
          created_at: new Date(),
          user: { name: 'Artist User' },
          rating: { average: 4.2, count: 8 }
        }
      ];

      // Filter by search term if provided
      let filteredPrompts = mockPrompts;
      const searchTerm = req.query.search as string;
      if (searchTerm && typeof searchTerm === 'string') {
        filteredPrompts = mockPrompts.filter(prompt =>
          prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Return properly formatted response
      res.status(200).json({
        prompts: filteredPrompts,
        totalPages: 1,
        currentPage: 1,
        totalPrompts: filteredPrompts.length
      });
    }
  } else if (req.method === 'POST') {
    try {
      // Authentication check for creating prompts
      const session = await getServerSession(req, res, authOptions);

      if (!session?.user?.id) {
        return res.status(401).json({ message: 'Authentication required to create prompts' });
      }

      // Validate required fields
      const { title, description, prompt_text, instructions, example, platform, category, tags } = req.body;

      if (!title?.trim()) {
        return res.status(400).json({ message: 'Title is required' });
      }

      if (!description?.trim()) {
        return res.status(400).json({ message: 'Description is required' });
      }

      if (!prompt_text?.trim()) {
        return res.status(400).json({ message: 'Prompt text is required' });
      }

      if (!platform) {
        return res.status(400).json({ message: 'Platform is required' });
      }

      if (!category) {
        return res.status(400).json({ message: 'Category is required' });
      }

      // Verify category exists
      const categoryDoc = await Category.findById(category);
      if (!categoryDoc) {
        return res.status(400).json({ message: 'Invalid category' });
      }

      // Create the prompt
      const promptData = {
        title: title.trim(),
        description: description.trim(),
        prompt_text: prompt_text.trim(),
        instructions: instructions?.trim() || '',
        example: example?.trim() || '',
        platform,
        category,
        tags: Array.isArray(tags) ? tags : [],
        created_by: session.user.id,
        verified: false, // New prompts need admin verification
        ratings: []
      };

      const prompt = await Prompt.create(promptData);
      const populatedPrompt = await Prompt.findById(prompt._id)
        .populate('category')
        .populate('created_by', '-password -activity_log');

      // Send email notifications to users who have enabled them
      try {
        await emailService.initialize();

        // Get users who want new prompt notifications
        const usersWithNotifications = await User.find({
          'preferences.emailNotifications': true
        }).select('email name');

        if (usersWithNotifications.length > 0) {
          const userEmails = usersWithNotifications.map(user => user.email);
          const promptUrl = `${process.env.NEXTAUTH_URL}/prompts/${prompt._id}`;

          await sendNewPromptNotification(userEmails, {
            title: populatedPrompt.title,
            description: populatedPrompt.description,
            platform: populatedPrompt.platform,
            category: populatedPrompt.category?.name || 'Uncategorized',
            url: promptUrl
          });
        }
      } catch (emailError) {
        console.error('Failed to send new prompt notifications:', emailError);
        // Don't fail the request if email sending fails
      }

      res.status(201).json(populatedPrompt);
    } catch (err) {
      console.error('Error creating prompt:', err);
      res.status(400).json({
        message: 'Failed to create prompt',
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}