import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import Prompt from '../../../../models/Prompt';
import User from '../../../../models/User';
import Analytics from '../../../../models/Analytics';
import { sendPromptRatingNotification, emailService } from '../../../../lib/emailService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { id } = req.query;
  const { value } = req.body;

  if (value !== 1 && value !== -1) {
    return res.status(400).json({ message: 'Rating value must be 1 or -1' });
  }

  try {
    await dbConnect();

    const prompt = await Prompt.findById(id);
    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if user has already rated this prompt
    const existingRatingIndex = prompt.ratings.findIndex(
      (rating: any) => rating.user.toString() === session.user.id
    );

    if (existingRatingIndex !== -1) {
      // Update existing rating
      prompt.ratings[existingRatingIndex].value = value;
    } else {
      // Add new rating
      prompt.ratings.push({
        user: session.user.id,
        value: value
      });
    }

    await prompt.save();

    // Send email notification to prompt creator
    try {
      await emailService.initialize();

      // Get the prompt creator's details
      const promptCreator = await User.findById(prompt.created_by).select('email name preferences');

      if (promptCreator && promptCreator.preferences?.emailNotifications) {
        const totalRatings = prompt.ratings.length;
        const totalRating = prompt.ratings.reduce((sum: number, rating: any) => sum + rating.value, 0);
        const averageRating = totalRating / totalRatings;
        const ratingType = value >= 4 ? 'positive' : value <= 2 ? 'negative' : 'neutral';
        const promptUrl = `${process.env.NEXTAUTH_URL}/prompts/${prompt._id}`;

        await sendPromptRatingNotification(promptCreator.email, {
          title: prompt.title,
          ratingType,
          currentRating: Math.round(averageRating * 10) / 10,
          totalRatings,
          url: promptUrl
        });
      }
    } catch (emailError) {
      console.error('Failed to send rating notification:', emailError);
      // Don't fail the request if email sending fails
    }

    // Log rating activity in analytics
    try {
      let analytics = await Analytics.findOne();
      if (!analytics) {
        analytics = new Analytics({
          prompt_views: [],
          search_queries: [],
          rating_activities: []
        });
      }

      analytics.rating_activities.push({
        prompt: id,
        user: session.user.id,
        value: value,
        timestamp: new Date()
      });

      await analytics.save();
    } catch (analyticsError) {
      console.error('Error logging rating activity:', analyticsError);
      // Don't fail the request if analytics logging fails
    }

    res.status(200).json({ 
      message: 'Rating updated successfully',
      totalRating: prompt.ratings.reduce((sum: number, rating: any) => sum + rating.value, 0)
    });
  } catch (error) {
    console.error('Error updating rating:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
