import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import { Prompt, Category, User } from '../../../../models';
import { retryDbOperation, isRetryableError } from '../../../../lib/db-retry';
import { Logger, getRequestContext } from '../../../../lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;
  const context = getRequestContext(req);

  Logger.info(`Processing request for prompt ${id}`, context);

  try {
    await dbConnect();
    Logger.debug('Database connection established', context);
  } catch (dbError) {
    Logger.error('Database connection failed', dbError, context);
    return res.status(500).json({
      message: 'Database connection failed',
      error: process.env.NODE_ENV === 'development' ? dbError : undefined
    });
  }

  if (req.method === 'GET') {
    try {
      // Validate ObjectId format
      if (!id || typeof id !== 'string' || !id.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ message: 'Invalid prompt ID format' });
      }

      const prompt = await retryDbOperation(async () => {
        return await Prompt.findById(id)
          .populate('category')
          .populate('created_by', '-password -activity_log');
      });

      if (!prompt) {
        return res.status(404).json({ message: 'Prompt not found' });
      }

      res.status(200).json(prompt);
    } catch (error) {
      Logger.error('Error fetching prompt', error, { ...context, promptId: id });

      // More specific error handling
      if (error instanceof Error && error.name === 'CastError') {
        return res.status(400).json({ message: 'Invalid prompt ID' });
      }

      res.status(500).json({
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  } else if (req.method === 'PUT') {
    // Update prompt (for admin/moderator use or prompt owner)
    try {
      // Check authentication
      const session = await getServerSession(req, res, authOptions);
      if (!session?.user?.id) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Find the prompt first to check ownership
      const existingPrompt = await Prompt.findById(id);
      if (!existingPrompt) {
        return res.status(404).json({ message: 'Prompt not found' });
      }

      // Check if user is the owner or an admin
      const isOwner = existingPrompt.created_by.toString() === session.user.id;
      const isAdmin = session.user.role === 'admin';

      if (!isOwner && !isAdmin) {
        return res.status(403).json({ message: 'You can only edit your own prompts' });
      }

      // If user is editing their own prompt, reset verification status
      const updateData = { ...req.body };
      if (isOwner && !isAdmin) {
        updateData.verified = false; // Reset verification when user edits
      }

      const updatedPrompt = await Prompt.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      )
        .populate('category')
        .populate('created_by', '-password -activity_log');

      res.status(200).json(updatedPrompt);
    } catch (error) {
      console.error('Error updating prompt:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'DELETE') {
    // Delete prompt (for admin/moderator use or prompt owner)
    try {
      // Check authentication
      const session = await getServerSession(req, res, authOptions);
      if (!session?.user?.id) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Find the prompt first to check ownership
      const prompt = await Prompt.findById(id);
      if (!prompt) {
        return res.status(404).json({ message: 'Prompt not found' });
      }

      // Check if user is the owner or an admin
      const isOwner = prompt.created_by.toString() === session.user.id;
      const isAdmin = session.user.role === 'admin';

      if (!isOwner && !isAdmin) {
        return res.status(403).json({ message: 'You can only delete your own prompts' });
      }

      // Delete the prompt
      await Prompt.findByIdAndDelete(id);

      res.status(200).json({ message: 'Prompt deleted successfully' });
    } catch (error) {
      console.error('Error deleting prompt:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
