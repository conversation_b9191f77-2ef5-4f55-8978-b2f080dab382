import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/mongodb';
import { Prompt, Category } from '../../../models';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();
  const { q } = req.query;
  if (!q) return res.status(400).json({ error: 'Missing query' });
  const filter = {
    $or: [
      { title: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } },
      { tags: { $regex: q, $options: 'i' } },
    ],
  };
  const prompts = await Prompt.find(filter).populate('category').sort({ createdAt: -1 });
  res.status(200).json(prompts);
} 