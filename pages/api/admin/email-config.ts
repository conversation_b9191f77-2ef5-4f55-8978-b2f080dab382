import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import EmailConfig from '../../../models/EmailConfig';
import { emailService } from '../../../lib/emailService';
import { initializeServices } from '../../../lib/initializeServices';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Initialize services on first API call
  initializeServices();

  const session = await getServerSession(req, res, authOptions);

  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  await dbConnect();

  if (req.method === 'GET') {
    try {
      // Get the active email configuration
      let config = await EmailConfig.findOne({ isActive: true });
      
      if (!config) {
        // Create default configuration if none exists, populate from env vars if available
        config = await EmailConfig.create({
          smtpHost: process.env.SMTP_HOST || '',
          smtpPort: parseInt(process.env.SMTP_PORT || '587'),
          smtpSecure: parseInt(process.env.SMTP_PORT || '587') === 465,
          smtpUser: process.env.SMTP_USER || '',
          smtpPassword: process.env.SMTP_PASSWORD || '',
          fromEmail: process.env.SMTP_FROM_EMAIL || '',
          fromName: process.env.SMTP_FROM_NAME || 'AI Prompt Library',
          replyToEmail: '',
          enableNotifications: !!(process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASSWORD),
          notificationTypes: {
            newPrompts: true,
            promptRatings: true,
            platformAnnouncements: true,
            userRegistration: true,
            promptVerification: true,
          },
          templates: {
            newPrompt: {
              subject: 'New Prompt Added: {{promptTitle}}',
              htmlTemplate: `
                <h2>New Prompt Added</h2>
                <p>A new prompt has been added to the AI Prompt Library:</p>
                <h3>{{promptTitle}}</h3>
                <p>{{promptDescription}}</p>
                <p><strong>Platform:</strong> {{promptPlatform}}</p>
                <p><strong>Category:</strong> {{promptCategory}}</p>
                <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Prompt</a></p>
              `,
              textTemplate: 'New Prompt Added: {{promptTitle}}\n\n{{promptDescription}}\n\nPlatform: {{promptPlatform}}\nCategory: {{promptCategory}}\n\nView at: {{promptUrl}}'
            },
            promptRating: {
              subject: 'Your prompt received a new rating',
              htmlTemplate: `
                <h2>Your Prompt Received a Rating</h2>
                <p>Your prompt "{{promptTitle}}" has received a new {{ratingType}} rating!</p>
                <p><strong>Current Rating:</strong> {{currentRating}}/5 ({{totalRatings}} ratings)</p>
                <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Prompt</a></p>
              `,
              textTemplate: 'Your prompt "{{promptTitle}}" received a new {{ratingType}} rating!\n\nCurrent Rating: {{currentRating}}/5 ({{totalRatings}} ratings)\n\nView at: {{promptUrl}}'
            },
            announcement: {
              subject: '{{announcementTitle}} - AI Prompt Library',
              htmlTemplate: `
                <h2>{{announcementTitle}}</h2>
                <div>{{announcementContent}}</div>
                <p><a href="{{platformUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Visit Platform</a></p>
              `,
              textTemplate: '{{announcementTitle}}\n\n{{announcementContent}}\n\nVisit: {{platformUrl}}'
            },
            welcome: {
              subject: 'Welcome to AI Prompt Library!',
              htmlTemplate: `
                <h2>Welcome to AI Prompt Library, {{userName}}!</h2>
                <p>Thank you for joining our community of AI prompt enthusiasts.</p>
                <p>You can now:</p>
                <ul>
                  <li>Browse and discover high-quality AI prompts</li>
                  <li>Create and share your own prompts</li>
                  <li>Rate and review prompts from the community</li>
                  <li>Organize prompts by categories and platforms</li>
                </ul>
                <p><a href="{{platformUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Start Exploring</a></p>
              `,
              textTemplate: 'Welcome to AI Prompt Library, {{userName}}!\n\nThank you for joining our community. You can now browse prompts, create your own, and engage with the community.\n\nStart exploring: {{platformUrl}}'
            },
            promptVerified: {
              subject: 'Your prompt has been verified!',
              htmlTemplate: `
                <h2>Prompt Verified!</h2>
                <p>Congratulations! Your prompt "{{promptTitle}}" has been verified by our moderation team.</p>
                <p>Verified prompts receive higher visibility and are marked with a verification badge.</p>
                <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Your Verified Prompt</a></p>
              `,
              textTemplate: 'Your prompt "{{promptTitle}}" has been verified!\n\nVerified prompts receive higher visibility and are marked with a verification badge.\n\nView at: {{promptUrl}}'
            }
          },
          testEmail: '',
          isActive: true,
          lastModifiedBy: session.user.id
        });
      }

      // Remove sensitive information before sending
      const configResponse = {
        ...config.toObject(),
        smtpPassword: config.smtpPassword ? '••••••••' : ''
      };

      res.status(200).json(configResponse);
    } catch (error) {
      console.error('Error fetching email config:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'PUT') {
    try {
      const {
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUser,
        smtpPassword,
        fromEmail,
        fromName,
        replyToEmail,
        enableNotifications,
        notificationTypes,
        templates,
        testEmail
      } = req.body;

      // Validate required fields
      if (!smtpHost || !smtpUser || !fromEmail || !fromName) {
        return res.status(400).json({ 
          message: 'SMTP host, user, from email, and from name are required' 
        });
      }

      // Get existing config or create new one
      let config = await EmailConfig.findOne({ isActive: true });
      
      const updateData: any = {
        smtpHost,
        smtpPort: parseInt(smtpPort) || 587,
        smtpSecure: Boolean(smtpSecure),
        smtpUser,
        fromEmail,
        fromName,
        replyToEmail: replyToEmail || '',
        enableNotifications: Boolean(enableNotifications),
        notificationTypes: notificationTypes || {
          newPrompts: true,
          promptRatings: true,
          platformAnnouncements: true,
          userRegistration: true,
          promptVerification: true,
        },
        templates: templates || {},
        testEmail: testEmail || '',
        isActive: true,
        lastModifiedBy: session.user.id
      };

      // Only update password if provided
      if (smtpPassword && smtpPassword !== '••••••••') {
        updateData.smtpPassword = smtpPassword;
      }

      if (config) {
        // Update existing config
        Object.assign(config, updateData);
        await config.save();
      } else {
        // Create new config
        config = await EmailConfig.create({
          ...updateData,
          smtpPassword: smtpPassword || ''
        });
      }

      // Refresh email service configuration
      await emailService.refreshConfig();

      // Remove sensitive information before sending response
      const configResponse = {
        ...config.toObject(),
        smtpPassword: config.smtpPassword ? '••••••••' : ''
      };

      res.status(200).json(configResponse);
    } catch (error) {
      console.error('Error updating email config:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.status(405).json({ message: 'Method not allowed' });
  }
}
