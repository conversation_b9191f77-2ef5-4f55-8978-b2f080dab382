import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import ScheduledAnnouncement from '../../../models/ScheduledAnnouncement';
import User from '../../../models/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  await dbConnect();

  switch (req.method) {
    case 'GET':
      return handleGet(req, res);
    case 'POST':
      return handlePost(req, res, session);
    case 'PUT':
      return handlePut(req, res, session);
    case 'DELETE':
      return handleDelete(req, res);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// Get all scheduled announcements
async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    
    const filter: any = {};
    if (status && status !== 'all') {
      filter.status = status;
    }

    const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
    
    const announcements = await ScheduledAnnouncement
      .find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit as string))
      .lean();

    const total = await ScheduledAnnouncement.countDocuments(filter);

    res.status(200).json({
      announcements,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string))
      }
    });
  } catch (error) {
    console.error('Error fetching scheduled announcements:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

// Create new scheduled announcement
async function handlePost(req: NextApiRequest, res: NextApiResponse, session: any) {
  try {
    const {
      title,
      subject,
      htmlContent,
      textContent,
      scheduledDate,
      timezone = 'UTC',
      recipientType,
      specificEmails = [],
      userRoles = []
    } = req.body;

    // Validate required fields
    if (!title || !subject || !htmlContent || !textContent || !scheduledDate || !recipientType) {
      return res.status(400).json({ 
        message: 'Title, subject, content, scheduled date, and recipient type are required' 
      });
    }

    // Validate scheduled date is in the future
    const scheduleDateTime = new Date(scheduledDate);
    if (scheduleDateTime <= new Date()) {
      return res.status(400).json({ 
        message: 'Scheduled date must be in the future' 
      });
    }

    // Calculate total recipients
    let totalRecipients = 0;
    if (recipientType === 'all_users') {
      totalRecipients = await User.countDocuments({});
    } else if (recipientType === 'specific_emails') {
      totalRecipients = specificEmails.length;
    } else if (recipientType === 'user_roles') {
      totalRecipients = await User.countDocuments({ role: { $in: userRoles } });
    }

    const announcement = await ScheduledAnnouncement.create({
      title,
      subject,
      htmlContent,
      textContent,
      scheduledDate: scheduleDateTime,
      timezone,
      recipientType,
      specificEmails,
      userRoles,
      status: 'scheduled',
      totalRecipients,
      createdBy: session.user.id,
      lastModifiedBy: session.user.id
    });

    res.status(201).json(announcement);
  } catch (error) {
    console.error('Error creating scheduled announcement:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

// Update scheduled announcement
async function handlePut(req: NextApiRequest, res: NextApiResponse, session: any) {
  try {
    const { id } = req.query;
    const {
      title,
      subject,
      htmlContent,
      textContent,
      scheduledDate,
      timezone,
      recipientType,
      specificEmails,
      userRoles,
      status
    } = req.body;

    const announcement = await ScheduledAnnouncement.findById(id);
    if (!announcement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    // Don't allow editing sent announcements
    if (announcement.status === 'sent') {
      return res.status(400).json({ message: 'Cannot edit sent announcements' });
    }

    // Update fields
    if (title !== undefined) announcement.title = title;
    if (subject !== undefined) announcement.subject = subject;
    if (htmlContent !== undefined) announcement.htmlContent = htmlContent;
    if (textContent !== undefined) announcement.textContent = textContent;
    if (scheduledDate !== undefined) {
      const scheduleDateTime = new Date(scheduledDate);
      if (scheduleDateTime <= new Date() && status === 'scheduled') {
        return res.status(400).json({ message: 'Scheduled date must be in the future' });
      }
      announcement.scheduledDate = scheduleDateTime;
    }
    if (timezone !== undefined) announcement.timezone = timezone;
    if (recipientType !== undefined) announcement.recipientType = recipientType;
    if (specificEmails !== undefined) announcement.specificEmails = specificEmails;
    if (userRoles !== undefined) announcement.userRoles = userRoles;
    if (status !== undefined) announcement.status = status;

    // Recalculate total recipients if recipient settings changed
    if (recipientType !== undefined || specificEmails !== undefined || userRoles !== undefined) {
      let totalRecipients = 0;
      if (announcement.recipientType === 'all_users') {
        totalRecipients = await User.countDocuments({});
      } else if (announcement.recipientType === 'specific_emails') {
        totalRecipients = announcement.specificEmails.length;
      } else if (announcement.recipientType === 'user_roles') {
        totalRecipients = await User.countDocuments({ role: { $in: announcement.userRoles } });
      }
      announcement.totalRecipients = totalRecipients;
    }

    announcement.lastModifiedBy = session.user.id;
    await announcement.save();

    res.status(200).json(announcement);
  } catch (error) {
    console.error('Error updating scheduled announcement:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

// Delete scheduled announcement
async function handleDelete(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;

    const announcement = await ScheduledAnnouncement.findById(id);
    if (!announcement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    // Don't allow deleting sent announcements
    if (announcement.status === 'sent') {
      return res.status(400).json({ message: 'Cannot delete sent announcements' });
    }

    await ScheduledAnnouncement.findByIdAndDelete(id);
    res.status(200).json({ message: 'Announcement deleted successfully' });
  } catch (error) {
    console.error('Error deleting scheduled announcement:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
