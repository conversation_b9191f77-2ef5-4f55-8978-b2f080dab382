import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import EmailConfig from '../../../models/EmailConfig';
import { emailService } from '../../../lib/emailService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  const { testEmail } = req.body;

  if (!testEmail) {
    return res.status(400).json({ message: 'Test email address is required' });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(testEmail)) {
    return res.status(400).json({ message: 'Invalid email address format' });
  }

  try {
    await dbConnect();

    // Initialize email service
    const initialized = await emailService.initialize();
    
    if (!initialized) {
      return res.status(400).json({ 
        message: 'Email service could not be initialized. Please check your SMTP configuration.' 
      });
    }

    // Send test email
    const success = await emailService.sendTestEmail(testEmail);

    if (success) {
      // Update the last test sent timestamp
      await EmailConfig.updateOne(
        { isActive: true },
        { 
          lastTestSent: new Date(),
          testEmail: testEmail
        }
      );

      res.status(200).json({ 
        message: 'Test email sent successfully!',
        sentTo: testEmail,
        sentAt: new Date().toISOString()
      });
    } else {
      res.status(500).json({ 
        message: 'Failed to send test email. Please check your SMTP configuration.' 
      });
    }
  } catch (error) {
    console.error('Error sending test email:', error);

    let errorMessage = 'Failed to send test email';

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('EAUTH')) {
        errorMessage = 'Authentication failed. Please check your SMTP username and password.';
      } else if (error.message.includes('ECONNECTION')) {
        errorMessage = 'Connection failed. Please check your SMTP host and port.';
      } else if (error.message.includes('ETIMEDOUT')) {
        errorMessage = 'Connection timed out. Please check your SMTP host and port.';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'SMTP host not found. Please check your SMTP host configuration.';
      }
    }

    res.status(500).json({
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' && error instanceof Error ? error.message : undefined
    });
  }
}
