import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { scheduledAnnouncementJob } from '../../../lib/scheduledAnnouncementJob';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  switch (req.method) {
    case 'GET':
      return handleGet(req, res);
    case 'POST':
      return handlePost(req, res);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// Get job status
async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const status = scheduledAnnouncementJob.getStatus();
    res.status(200).json(status);
  } catch (error) {
    console.error('Error getting job status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

// Control job (start/stop/trigger)
async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { action } = req.body;

    switch (action) {
      case 'start':
        scheduledAnnouncementJob.start();
        res.status(200).json({ message: 'Job started successfully' });
        break;
      
      case 'stop':
        scheduledAnnouncementJob.stop();
        res.status(200).json({ message: 'Job stopped successfully' });
        break;
      
      case 'trigger':
        await scheduledAnnouncementJob.triggerCheck();
        res.status(200).json({ message: 'Manual check triggered successfully' });
        break;
      
      default:
        res.status(400).json({ message: 'Invalid action. Use start, stop, or trigger' });
    }
  } catch (error) {
    console.error('Error controlling job:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
