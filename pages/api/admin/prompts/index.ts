import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import dbConnect from '../../../../lib/mongodb';
import { Prompt, Category, User } from '../../../../models';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    // Check authentication and authorization
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user is admin or moderator
    if (session.user.role !== 'admin' && session.user.role !== 'moderator') {
      return res.status(403).json({ error: 'Admin or moderator access required' });
    }

    await dbConnect();

    const {
      verified,
      platform,
      category,
      search,
      sort = 'newest',
      page = '1',
      limit = '50'
    } = req.query;

    // Build filter object
    const filter: any = {};

    // Verification filter
    if (verified === 'true') {
      filter.verified = true;
    } else if (verified === 'false') {
      filter.verified = false;
    }

    // Platform filter
    if (platform && platform !== 'All Platforms') {
      filter.platform = platform;
    }

    // Category filter
    if (category && category !== 'All Categories') {
      // Find category by name
      const categoryDoc = await Category.findOne({ name: category });
      if (categoryDoc) {
        filter.category = categoryDoc._id;
      }
    }

    // Search filter
    if (search && (search as string).length >= 2) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search as string, 'i')] } },
      ];
    }

    // Sorting
    let sortOption: any = { createdAt: -1 }; // default: newest first
    switch (sort) {
      case 'oldest':
        sortOption = { createdAt: 1 };
        break;
      case 'title':
        sortOption = { title: 1 };
        break;
      case 'verified':
        sortOption = { verified: -1, createdAt: -1 };
        break;
      default:
        sortOption = { createdAt: -1 };
    }

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Get prompts with populated fields
    const prompts = await Prompt.find(filter)
      .populate('category', 'name')
      .populate('created_by', 'name email')
      .sort(sortOption)
      .skip(skip)
      .limit(limitNum)
      .lean();

    // Transform the data to match expected format
    const transformedPrompts = prompts.map((prompt: any) => ({
      _id: prompt._id?.toString() || '',
      title: prompt.title,
      description: prompt.description,
      prompt_text: prompt.prompt_text,
      platform: prompt.platform,
      category: {
        _id: prompt.category?._id?.toString() || '',
        name: prompt.category?.name || 'Uncategorized'
      },
      tags: prompt.tags || [],
      verified: prompt.verified || false,
      created_by: {
        _id: prompt.created_by._id.toString(),
        name: prompt.created_by.name || 'Anonymous',
        email: prompt.created_by.email || ''
      },
      createdAt: prompt.createdAt,
      ratings: prompt.ratings || []
    }));

    res.status(200).json(transformedPrompts);

  } catch (error) {
    console.error('Error fetching admin prompts:', error);
    res.status(500).json({
      error: 'Failed to fetch prompts',
      message: 'Internal server error'
    });
  }
}
