import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import User from '../../../models/User';
import { sendAnnouncementEmail, emailService } from '../../../lib/emailService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  const { title, content, sendToAll } = req.body;

  if (!title || !content) {
    return res.status(400).json({ message: 'Title and content are required' });
  }

  try {
    await dbConnect();

    // Initialize email service
    const initialized = await emailService.initialize();
    
    if (!initialized) {
      return res.status(400).json({ 
        message: 'Email service could not be initialized. Please check your email configuration.' 
      });
    }

    // Get users who want announcement notifications
    const filter: any = {
      'preferences.emailNotifications': true
    };

    const usersWithNotifications = await User.find(filter).select('email name');

    if (usersWithNotifications.length === 0) {
      return res.status(200).json({ 
        message: 'No users have email notifications enabled',
        sentTo: 0
      });
    }

    const userEmails = usersWithNotifications.map(user => user.email);
    const platformUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

    const success = await sendAnnouncementEmail(userEmails, {
      title,
      content,
      platformUrl
    });

    if (success) {
      res.status(200).json({ 
        message: 'Announcement sent successfully!',
        sentTo: userEmails.length,
        recipients: userEmails
      });
    } else {
      res.status(500).json({ 
        message: 'Failed to send announcement. Please check your email configuration.' 
      });
    }
  } catch (error) {
    console.error('Error sending announcement:', error);
    res.status(500).json({
      message: 'Failed to send announcement',
      error: process.env.NODE_ENV === 'development' && error instanceof Error ? error.message : undefined
    });
  }
}
