import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import User from '../../../models/User';
import ScheduledAnnouncement from '../../../models/ScheduledAnnouncement';
import { sendAnnouncementEmail, emailService } from '../../../lib/emailService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user?.role || session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  const { announcementId, title, content, sendToAll } = req.body;

  // Handle both scheduled announcement sending and direct announcement sending
  if (announcementId) {
    return handleScheduledAnnouncement(req, res, session);
  }

  if (!title || !content) {
    return res.status(400).json({ message: 'Title and content are required' });
  }

  try {
    await dbConnect();

    // Initialize email service
    const initialized = await emailService.initialize();
    
    if (!initialized) {
      return res.status(400).json({ 
        message: 'Email service could not be initialized. Please check your email configuration.' 
      });
    }

    // Get users who want announcement notifications
    const filter: any = {
      'preferences.emailNotifications': true
    };

    const usersWithNotifications = await User.find(filter).select('email name');

    if (usersWithNotifications.length === 0) {
      return res.status(200).json({ 
        message: 'No users have email notifications enabled',
        sentTo: 0
      });
    }

    const userEmails = usersWithNotifications.map(user => user.email);
    const platformUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

    const success = await sendAnnouncementEmail(userEmails, {
      title,
      content,
      platformUrl
    });

    if (success) {
      res.status(200).json({ 
        message: 'Announcement sent successfully!',
        sentTo: userEmails.length,
        recipients: userEmails
      });
    } else {
      res.status(500).json({ 
        message: 'Failed to send announcement. Please check your email configuration.' 
      });
    }
  } catch (error) {
    console.error('Error sending announcement:', error);
    res.status(500).json({
      message: 'Failed to send announcement',
      error: process.env.NODE_ENV === 'development' && error instanceof Error ? error.message : undefined
    });
  }

async function handleScheduledAnnouncement(req: NextApiRequest, res: NextApiResponse, session: any) {
  const { announcementId } = req.body;

  try {
    await dbConnect();

    const announcement = await ScheduledAnnouncement.findById(announcementId);
    if (!announcement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    if (announcement.status === 'sent') {
      return res.status(400).json({ message: 'Announcement has already been sent' });
    }

    if (announcement.status === 'cancelled') {
      return res.status(400).json({ message: 'Cannot send cancelled announcement' });
    }

    // Initialize email service
    const initialized = await emailService.initialize();
    if (!initialized) {
      return res.status(500).json({
        message: 'Email service could not be initialized. Please check your SMTP configuration.'
      });
    }

    // Get recipients based on type
    let recipients: string[] = [];

    if (announcement.recipientType === 'all_users') {
      const users = await User.find({}, 'email').lean();
      recipients = users.map(user => user.email).filter(email => email);
    } else if (announcement.recipientType === 'specific_emails') {
      recipients = announcement.specificEmails;
    } else if (announcement.recipientType === 'user_roles') {
      const users = await User.find({ role: { $in: announcement.userRoles } }, 'email').lean();
      recipients = users.map(user => user.email).filter(email => email);
    }

    if (recipients.length === 0) {
      return res.status(400).json({ message: 'No recipients found for this announcement' });
    }

    // Update announcement status
    announcement.status = 'scheduled';
    announcement.totalRecipients = recipients.length;
    announcement.lastModifiedBy = session.user.id;
    await announcement.save();

    // Send emails in batches
    let successfulDeliveries = 0;
    let failedDeliveries = 0;
    const errors: string[] = [];

    const batchSize = 10;
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      for (const email of batch) {
        try {
          const success = await emailService.sendEmail({
            to: email,
            subject: announcement.subject,
            html: announcement.htmlContent,
            text: announcement.textContent
          });

          if (success) {
            successfulDeliveries++;
          } else {
            failedDeliveries++;
            errors.push(`Failed to send to ${email}`);
          }
        } catch (error) {
          failedDeliveries++;
          errors.push(`Error sending to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Small delay between batches
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Update announcement with results
    announcement.successfulDeliveries = successfulDeliveries;
    announcement.failedDeliveries = failedDeliveries;
    announcement.sentAt = new Date();

    if (failedDeliveries === 0) {
      announcement.status = 'sent';
    } else if (successfulDeliveries === 0) {
      announcement.status = 'failed';
      announcement.failureReason = errors.join('; ');
    } else {
      announcement.status = 'sent';
      announcement.failureReason = `Partial failure: ${errors.slice(0, 5).join('; ')}${errors.length > 5 ? '...' : ''}`;
    }

    await announcement.save();

    return res.status(200).json({
      message: 'Announcement sent successfully',
      results: {
        totalRecipients: recipients.length,
        successfulDeliveries,
        failedDeliveries,
        deliveryRate: ((successfulDeliveries / recipients.length) * 100).toFixed(2) + '%'
      },
      announcement: {
        id: announcement._id,
        title: announcement.title,
        status: announcement.status,
        sentAt: announcement.sentAt
      }
    });

  } catch (error) {
    console.error('Error sending scheduled announcement:', error);

    try {
      await ScheduledAnnouncement.findByIdAndUpdate(announcementId, {
        status: 'failed',
        failureReason: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } catch (updateError) {
      console.error('Error updating announcement status:', updateError);
    }

    return res.status(500).json({
      message: 'Failed to send announcement',
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
}
}
