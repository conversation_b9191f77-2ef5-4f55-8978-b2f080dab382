import nodemailer from 'nodemailer';
import EmailConfig, { IEmailConfig } from '../models/EmailConfig';
import dbConnect from './mongodb';

export interface EmailData {
  to: string | string[];
  subject: string;
  html: string;
  text: string;
  replyTo?: string;
}

export interface TemplateVariables {
  [key: string]: string | number | boolean;
}

export class EmailService {
  private static instance: EmailService;
  private transporter: nodemailer.Transporter | null = null;
  private config: IEmailConfig | null = null;

  private constructor() {}

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Initialize the email service with the active configuration
   */
  public async initialize(): Promise<boolean> {
    try {
      await dbConnect();

      // Get the active email configuration
      this.config = await EmailConfig.findOne({ isActive: true });

      let smtpConfig: any;

      if (this.config && this.config.enableNotifications) {
        // Use database configuration
        smtpConfig = {
          host: this.config.smtpHost,
          port: this.config.smtpPort,
          secure: this.config.smtpSecure,
          auth: {
            user: this.config.smtpUser,
            pass: this.config.smtpPassword,
          }
        };
        console.log('Using database email configuration');
      } else {
        // Fall back to environment variables
        const envHost = process.env.SMTP_HOST;
        const envPort = process.env.SMTP_PORT;
        const envUser = process.env.SMTP_USER;
        const envPassword = process.env.SMTP_PASSWORD;

        if (!envHost || !envUser || !envPassword) {
          console.log('No email configuration found in database or environment variables');
          return false;
        }

        smtpConfig = {
          host: envHost,
          port: parseInt(envPort || '587'),
          secure: parseInt(envPort || '587') === 465,
          auth: {
            user: envUser,
            pass: envPassword,
          }
        };
        console.log('Using environment variable email configuration');
      }

      // Create transporter with SMTP configuration
      this.transporter = nodemailer.createTransport({
        ...smtpConfig,
        tls: {
          rejectUnauthorized: false // Allow self-signed certificates
        }
      });

      // Verify the connection
      await this.transporter.verify();
      console.log('Email service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize email service:', error);
      this.transporter = null;
      this.config = null;
      return false;
    }
  }

  /**
   * Send a test email
   */
  public async sendTestEmail(testEmail: string): Promise<boolean> {
    if (!this.transporter) {
      throw new Error('Email service not initialized');
    }

    const emailData: EmailData = {
      to: testEmail,
      subject: 'Test Email - AI Prompt Library',
      html: `
        <h2>Test Email</h2>
        <p>This is a test email from the AI Prompt Library email system.</p>
        <p>If you received this email, your SMTP configuration is working correctly!</p>
        <p><strong>Sent at:</strong> ${new Date().toLocaleString()}</p>
      `,
      text: `Test Email - AI Prompt Library\n\nThis is a test email from the AI Prompt Library email system.\nIf you received this email, your SMTP configuration is working correctly!\n\nSent at: ${new Date().toLocaleString()}`
    };

    return this.sendEmail(emailData);
  }

  /**
   * Send an email using a template
   */
  public async sendTemplateEmail(
    templateType: keyof IEmailConfig['templates'],
    to: string | string[],
    variables: TemplateVariables
  ): Promise<boolean> {
    if (!this.transporter) {
      console.log('Email service not initialized');
      return false;
    }

    // If no config, skip template emails (only test emails work with env vars)
    if (!this.config) {
      console.log('No email configuration found, skipping template email');
      return false;
    }

    const template = this.config.templates[templateType];
    if (!template) {
      throw new Error(`Template ${templateType} not found`);
    }

    // Replace template variables
    const subject = this.replaceVariables(template.subject, variables);
    const html = this.replaceVariables(template.htmlTemplate, variables);
    const text = this.replaceVariables(template.textTemplate, variables);

    const emailData: EmailData = {
      to,
      subject,
      html,
      text,
      replyTo: this.config.replyToEmail || this.config.fromEmail
    };

    return this.sendEmail(emailData);
  }

  /**
   * Send a raw email
   */
  public async sendEmail(emailData: EmailData): Promise<boolean> {
    if (!this.transporter) {
      console.log('Email service not initialized');
      return false;
    }

    try {
      // Get from email and name from config or environment variables
      const fromEmail = this.config?.fromEmail || process.env.SMTP_FROM_EMAIL || 'noreply@localhost';
      const fromName = this.config?.fromName || process.env.SMTP_FROM_NAME || 'AI Prompt Library';
      const replyToEmail = this.config?.replyToEmail || fromEmail;

      const mailOptions = {
        from: `${fromName} <${fromEmail}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
        replyTo: emailData.replyTo || replyToEmail
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  /**
   * Replace template variables in a string
   */
  private replaceVariables(template: string, variables: TemplateVariables): string {
    let result = template;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    });

    return result;
  }

  /**
   * Check if notifications are enabled for a specific type
   */
  public isNotificationEnabled(type: keyof IEmailConfig['notificationTypes']): boolean {
    return this.config?.enableNotifications && this.config?.notificationTypes[type] || false;
  }

  /**
   * Get the current configuration
   */
  public getConfig(): IEmailConfig | null {
    return this.config;
  }

  /**
   * Send bulk emails with batch processing
   */
  public async sendBulkEmails(
    recipients: string[],
    emailData: Omit<EmailData, 'to'>,
    batchSize: number = 10,
    delayBetweenBatches: number = 1000
  ): Promise<{ successful: number; failed: number; errors: string[] }> {
    if (!this.transporter) {
      throw new Error('Email service not initialized');
    }

    let successful = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process in batches
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      // Send emails in parallel within the batch
      const batchPromises = batch.map(async (email) => {
        try {
          const success = await this.sendEmail({
            ...emailData,
            to: email
          });

          if (success) {
            successful++;
          } else {
            failed++;
            errors.push(`Failed to send to ${email}`);
          }
        } catch (error) {
          failed++;
          errors.push(`Error sending to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      await Promise.all(batchPromises);

      // Add delay between batches (except for the last batch)
      if (i + batchSize < recipients.length && delayBetweenBatches > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return { successful, failed, errors };
  }

  /**
   * Refresh the configuration from database
   */
  public async refreshConfig(): Promise<boolean> {
    return this.initialize();
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();

// Notification helper functions
export const sendNewPromptNotification = async (
  userEmails: string[],
  promptData: {
    title: string;
    description: string;
    platform: string;
    category: string;
    url: string;
  }
) => {
  if (!emailService.isNotificationEnabled('newPrompts')) {
    return false;
  }

  return emailService.sendTemplateEmail('newPrompt', userEmails, {
    promptTitle: promptData.title,
    promptDescription: promptData.description,
    promptPlatform: promptData.platform,
    promptCategory: promptData.category,
    promptUrl: promptData.url
  });
};

export const sendPromptRatingNotification = async (
  userEmail: string,
  promptData: {
    title: string;
    ratingType: string;
    currentRating: number;
    totalRatings: number;
    url: string;
  }
) => {
  if (!emailService.isNotificationEnabled('promptRatings')) {
    return false;
  }

  return emailService.sendTemplateEmail('promptRating', userEmail, {
    promptTitle: promptData.title,
    ratingType: promptData.ratingType,
    currentRating: promptData.currentRating,
    totalRatings: promptData.totalRatings,
    promptUrl: promptData.url
  });
};

export const sendWelcomeEmail = async (
  userEmail: string,
  userName: string,
  platformUrl: string
) => {
  if (!emailService.isNotificationEnabled('userRegistration')) {
    return false;
  }

  return emailService.sendTemplateEmail('welcome', userEmail, {
    userName,
    platformUrl
  });
};

export const sendPromptVerifiedNotification = async (
  userEmail: string,
  promptData: {
    title: string;
    url: string;
  }
) => {
  if (!emailService.isNotificationEnabled('promptVerification')) {
    return false;
  }

  return emailService.sendTemplateEmail('promptVerified', userEmail, {
    promptTitle: promptData.title,
    promptUrl: promptData.url
  });
};

export const sendAnnouncementEmail = async (
  userEmails: string[],
  announcementData: {
    title: string;
    content: string;
    platformUrl: string;
  }
) => {
  if (!emailService.isNotificationEnabled('platformAnnouncements')) {
    return false;
  }

  return emailService.sendTemplateEmail('announcement', userEmails, {
    announcementTitle: announcementData.title,
    announcementContent: announcementData.content,
    platformUrl: announcementData.platformUrl
  });
};
