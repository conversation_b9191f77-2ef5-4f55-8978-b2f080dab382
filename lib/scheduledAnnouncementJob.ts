import dbConnect from './mongodb';
import ScheduledAnnouncement from '../models/ScheduledAnnouncement';
import User from '../models/User';
import { emailService } from './emailService';

class ScheduledAnnouncementJob {
  private static instance: ScheduledAnnouncementJob;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly checkInterval = 60000; // Check every minute

  private constructor() {}

  public static getInstance(): ScheduledAnnouncementJob {
    if (!ScheduledAnnouncementJob.instance) {
      ScheduledAnnouncementJob.instance = new ScheduledAnnouncementJob();
    }
    return ScheduledAnnouncementJob.instance;
  }

  /**
   * Start the background job
   */
  public start(): void {
    if (this.isRunning) {
      console.log('Scheduled announcement job is already running');
      return;
    }

    console.log('Starting scheduled announcement job...');
    this.isRunning = true;
    
    // Run immediately on start
    this.checkAndSendAnnouncements();
    
    // Then run every minute
    this.intervalId = setInterval(() => {
      this.checkAndSendAnnouncements();
    }, this.checkInterval);
  }

  /**
   * Stop the background job
   */
  public stop(): void {
    if (!this.isRunning) {
      console.log('Scheduled announcement job is not running');
      return;
    }

    console.log('Stopping scheduled announcement job...');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Check for due announcements and send them
   */
  private async checkAndSendAnnouncements(): Promise<void> {
    try {
      await dbConnect();

      // Find announcements that are due to be sent
      const dueAnnouncements = await ScheduledAnnouncement.find({
        status: 'scheduled',
        scheduledDate: { $lte: new Date() }
      }).sort({ scheduledDate: 1 });

      if (dueAnnouncements.length === 0) {
        return;
      }

      console.log(`Found ${dueAnnouncements.length} due announcements to send`);

      // Process each announcement
      for (const announcement of dueAnnouncements) {
        await this.sendAnnouncement(announcement);
      }

    } catch (error) {
      console.error('Error checking for due announcements:', error);
    }
  }

  /**
   * Send a specific announcement
   */
  private async sendAnnouncement(announcement: any): Promise<void> {
    try {
      console.log(`Sending announcement: ${announcement.title}`);

      // Initialize email service
      const initialized = await emailService.initialize();
      if (!initialized) {
        throw new Error('Email service could not be initialized');
      }

      // Get recipients based on type
      let recipients: string[] = [];
      
      if (announcement.recipientType === 'all_users') {
        const users = await User.find({}, 'email').lean();
        recipients = users.map(user => user.email).filter(email => email);
      } else if (announcement.recipientType === 'specific_emails') {
        recipients = announcement.specificEmails;
      } else if (announcement.recipientType === 'user_roles') {
        const users = await User.find({ role: { $in: announcement.userRoles } }, 'email').lean();
        recipients = users.map(user => user.email).filter(email => email);
      }

      if (recipients.length === 0) {
        throw new Error('No recipients found for this announcement');
      }

      // Update announcement status to indicate sending is in progress
      announcement.status = 'scheduled'; // Keep as scheduled while sending
      announcement.totalRecipients = recipients.length;
      await announcement.save();

      // Send emails using bulk email method
      const results = await emailService.sendBulkEmails(
        recipients,
        {
          subject: announcement.subject,
          html: announcement.htmlContent,
          text: announcement.textContent
        },
        10, // batch size
        1000 // delay between batches (1 second)
      );

      // Update announcement with results
      announcement.successfulDeliveries = results.successful;
      announcement.failedDeliveries = results.failed;
      announcement.sentAt = new Date();

      if (results.failed === 0) {
        announcement.status = 'sent';
        console.log(`✅ Announcement "${announcement.title}" sent successfully to ${results.successful} recipients`);
      } else if (results.successful === 0) {
        announcement.status = 'failed';
        announcement.failureReason = results.errors.join('; ');
        console.error(`❌ Announcement "${announcement.title}" failed to send to all recipients`);
      } else {
        announcement.status = 'sent'; // Partial success still counts as sent
        announcement.failureReason = `Partial failure: ${results.errors.slice(0, 5).join('; ')}${results.errors.length > 5 ? '...' : ''}`;
        console.warn(`⚠️ Announcement "${announcement.title}" sent with partial success: ${results.successful}/${recipients.length} delivered`);
      }

      await announcement.save();

    } catch (error) {
      console.error(`Error sending announcement "${announcement.title}":`, error);
      
      // Update announcement status to failed
      try {
        announcement.status = 'failed';
        announcement.failureReason = error instanceof Error ? error.message : 'Unknown error occurred';
        await announcement.save();
      } catch (updateError) {
        console.error('Error updating announcement status:', updateError);
      }
    }
  }

  /**
   * Get the current status of the job
   */
  public getStatus(): { isRunning: boolean; checkInterval: number } {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval
    };
  }

  /**
   * Manually trigger a check (useful for testing)
   */
  public async triggerCheck(): Promise<void> {
    console.log('Manually triggering announcement check...');
    await this.checkAndSendAnnouncements();
  }
}

// Export singleton instance
export const scheduledAnnouncementJob = ScheduledAnnouncementJob.getInstance();

// Auto-start the job in production
if (process.env.NODE_ENV === 'production') {
  scheduledAnnouncementJob.start();
  console.log('Scheduled announcement job auto-started in production mode');
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, stopping scheduled announcement job...');
  scheduledAnnouncementJob.stop();
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, stopping scheduled announcement job...');
  scheduledAnnouncementJob.stop();
});
