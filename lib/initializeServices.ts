import { scheduledAnnouncementJob } from './scheduledAnnouncementJob';

let initialized = false;

/**
 * Initialize all background services
 * This should be called once when the application starts
 */
export function initializeServices() {
  if (initialized) {
    return;
  }

  console.log('Initializing background services...');

  // Start the scheduled announcement job
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_ANNOUNCEMENT_JOB === 'true') {
    scheduledAnnouncementJob.start();
    console.log('✅ Scheduled announcement job started');
  } else {
    console.log('ℹ️ Scheduled announcement job not started (not in production mode)');
    console.log('   Set ENABLE_ANNOUNCEMENT_JOB=true to enable in development');
  }

  initialized = true;
  console.log('✅ Background services initialized');
}

/**
 * Get initialization status
 */
export function isInitialized(): boolean {
  return initialized;
}

/**
 * Force re-initialization (useful for testing)
 */
export function forceReinitialize() {
  initialized = false;
  initializeServices();
}
