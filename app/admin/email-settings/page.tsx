'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface EmailConfig {
  _id?: string;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUser: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  replyToEmail: string;
  enableNotifications: boolean;
  notificationTypes: {
    newPrompts: boolean;
    promptRatings: boolean;
    platformAnnouncements: boolean;
    userRegistration: boolean;
    promptVerification: boolean;
  };
  testEmail: string;
  lastTestSent?: string;
}

interface ScheduledAnnouncement {
  _id?: string;
  title: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  scheduledDate: string;
  timezone: string;
  recipientType: 'all_users' | 'specific_emails' | 'user_roles';
  specificEmails: string[];
  userRoles: string[];
  status: 'draft' | 'scheduled' | 'sent' | 'failed' | 'cancelled';
  sentAt?: string;
  totalRecipients: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  createdAt: string;
  updatedAt: string;
}

export default function EmailSettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [config, setConfig] = useState<EmailConfig>({
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: 'AI Prompt Library',
    replyToEmail: '',
    enableNotifications: false,
    notificationTypes: {
      newPrompts: true,
      promptRatings: true,
      platformAnnouncements: true,
      userRegistration: true,
      promptVerification: true,
    },
    testEmail: ''
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Scheduled Announcements state
  const [announcements, setAnnouncements] = useState<ScheduledAnnouncement[]>([]);
  const [showAnnouncementForm, setShowAnnouncementForm] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<ScheduledAnnouncement | null>(null);
  const [jobStatus, setJobStatus] = useState<{ isRunning: boolean; checkInterval: number } | null>(null);
  const [announcementForm, setAnnouncementForm] = useState({
    title: '',
    subject: '',
    htmlContent: '',
    textContent: '',
    scheduledDate: '',
    scheduledTime: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    recipientType: 'all_users' as 'all_users' | 'specific_emails' | 'user_roles',
    specificEmails: '',
    userRoles: [] as string[]
  });
  const [activeTab, setActiveTab] = useState<'smtp' | 'notifications' | 'announcements' | 'test'>('smtp');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user?.role || session.user.role !== 'admin') {
      router.push('/admin');
      return;
    }

    fetchConfig();
  }, [session, status, router]);

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/admin/email-config');
      if (response.ok) {
        const data = await response.json();
        setConfig(data);
      } else {
        setError('Failed to load email configuration');
      }
    } catch (err) {
      setError('Failed to load email configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      
      if (name.startsWith('notificationTypes.')) {
        const notificationType = name.split('.')[1];
        setConfig(prev => ({
          ...prev,
          notificationTypes: {
            ...prev.notificationTypes,
            [notificationType]: checked
          }
        }));
      } else {
        setConfig(prev => ({ ...prev, [name]: checked }));
      }
    } else {
      setConfig(prev => ({ 
        ...prev, 
        [name]: type === 'number' ? parseInt(value) || 0 : value 
      }));
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/admin/email-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (response.ok) {
        setConfig(data);
        setSuccess('Email configuration saved successfully!');
      } else {
        setError(data.message || 'Failed to save configuration');
      }
    } catch (err) {
      setError('Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!config.testEmail) {
      setError('Please enter a test email address');
      return;
    }

    setTesting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/admin/email-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testEmail: config.testEmail }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Test email sent successfully to ${config.testEmail}!`);
        // Refresh config to get updated lastTestSent
        fetchConfig();
      } else {
        setError(data.message || 'Failed to send test email');
      }
    } catch (err) {
      setError('Failed to send test email');
    } finally {
      setTesting(false);
    }
  };

  // Scheduled Announcements functions
  const fetchAnnouncements = async () => {
    try {
      const response = await fetch('/api/admin/scheduled-announcements');
      if (response.ok) {
        const data = await response.json();
        setAnnouncements(data.announcements || []);
      }
    } catch (err) {
      console.error('Failed to fetch announcements:', err);
    }
  };

  const handleAnnouncementSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      const scheduledDateTime = new Date(`${announcementForm.scheduledDate}T${announcementForm.scheduledTime}`);

      const payload = {
        title: announcementForm.title,
        subject: announcementForm.subject,
        htmlContent: announcementForm.htmlContent,
        textContent: announcementForm.textContent,
        scheduledDate: scheduledDateTime.toISOString(),
        timezone: announcementForm.timezone,
        recipientType: announcementForm.recipientType,
        specificEmails: announcementForm.recipientType === 'specific_emails'
          ? announcementForm.specificEmails.split(',').map(email => email.trim()).filter(email => email)
          : [],
        userRoles: announcementForm.userRoles
      };

      const url = editingAnnouncement
        ? `/api/admin/scheduled-announcements?id=${editingAnnouncement._id}`
        : '/api/admin/scheduled-announcements';

      const method = editingAnnouncement ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        setSuccess(editingAnnouncement ? 'Announcement updated successfully!' : 'Announcement scheduled successfully!');
        setShowAnnouncementForm(false);
        setEditingAnnouncement(null);
        resetAnnouncementForm();
        fetchAnnouncements();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to save announcement');
      }
    } catch (err) {
      setError('Failed to save announcement');
    }
  };

  const resetAnnouncementForm = () => {
    setAnnouncementForm({
      title: '',
      subject: '',
      htmlContent: '',
      textContent: '',
      scheduledDate: '',
      scheduledTime: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      recipientType: 'all_users',
      specificEmails: '',
      userRoles: []
    });
  };

  const handleEditAnnouncement = (announcement: ScheduledAnnouncement) => {
    const scheduledDate = new Date(announcement.scheduledDate);
    setAnnouncementForm({
      title: announcement.title,
      subject: announcement.subject,
      htmlContent: announcement.htmlContent,
      textContent: announcement.textContent,
      scheduledDate: scheduledDate.toISOString().split('T')[0],
      scheduledTime: scheduledDate.toTimeString().slice(0, 5),
      timezone: announcement.timezone,
      recipientType: announcement.recipientType,
      specificEmails: announcement.specificEmails.join(', '),
      userRoles: announcement.userRoles
    });
    setEditingAnnouncement(announcement);
    setShowAnnouncementForm(true);
  };

  const handleDeleteAnnouncement = async (id: string) => {
    if (!confirm('Are you sure you want to delete this announcement?')) return;

    try {
      const response = await fetch(`/api/admin/scheduled-announcements?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setSuccess('Announcement deleted successfully!');
        fetchAnnouncements();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete announcement');
      }
    } catch (err) {
      setError('Failed to delete announcement');
    }
  };

  const handleSendNow = async (id: string) => {
    if (!confirm('Are you sure you want to send this announcement now?')) return;

    try {
      const response = await fetch('/api/admin/send-announcement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ announcementId: id })
      });

      if (response.ok) {
        const data = await response.json();
        setSuccess(`Announcement sent successfully! ${data.results.successfulDeliveries}/${data.results.totalRecipients} emails delivered.`);
        fetchAnnouncements();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to send announcement');
      }
    } catch (err) {
      setError('Failed to send announcement');
    }
  };

  const fetchJobStatus = async () => {
    try {
      const response = await fetch('/api/admin/announcement-job');
      if (response.ok) {
        const data = await response.json();
        setJobStatus(data);
      }
    } catch (err) {
      console.error('Failed to fetch job status:', err);
    }
  };

  const handleJobAction = async (action: 'start' | 'stop' | 'trigger') => {
    try {
      const response = await fetch('/api/admin/announcement-job', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      });

      if (response.ok) {
        const data = await response.json();
        setSuccess(data.message);
        fetchJobStatus();
      } else {
        const data = await response.json();
        setError(data.message || `Failed to ${action} job`);
      }
    } catch (err) {
      setError(`Failed to ${action} job`);
    }
  };

  // Fetch announcements and job status when the announcements tab is active
  React.useEffect(() => {
    if (activeTab === 'announcements') {
      fetchAnnouncements();
      fetchJobStatus();
    }
  }, [activeTab]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading email settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Email Settings
              </h1>
              <p className="mt-1 text-gray-600">Configure SMTP settings and email notifications</p>
            </div>
          </div>
        </div>

        {/* Alert Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {success}
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'smtp', label: 'SMTP Configuration', icon: '⚙️' },
                { id: 'notifications', label: 'Notification Settings', icon: '🔔' },
                { id: 'announcements', label: 'Scheduled Announcements', icon: '📅' },
                { id: 'test', label: 'Test Email', icon: '✉️' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* SMTP Configuration Tab */}
            {activeTab === 'smtp' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SMTP Host *
                    </label>
                    <input
                      type="text"
                      name="smtpHost"
                      value={config.smtpHost}
                      onChange={handleInputChange}
                      placeholder="smtp.gmail.com"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SMTP Port *
                    </label>
                    <input
                      type="number"
                      name="smtpPort"
                      value={config.smtpPort}
                      onChange={handleInputChange}
                      placeholder="587"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SMTP Username *
                    </label>
                    <input
                      type="text"
                      name="smtpUser"
                      value={config.smtpUser}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SMTP Password *
                    </label>
                    <input
                      type="password"
                      name="smtpPassword"
                      value={config.smtpPassword}
                      onChange={handleInputChange}
                      placeholder="••••••••"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      From Email *
                    </label>
                    <input
                      type="email"
                      name="fromEmail"
                      value={config.fromEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      From Name *
                    </label>
                    <input
                      type="text"
                      name="fromName"
                      value={config.fromName}
                      onChange={handleInputChange}
                      placeholder="AI Prompt Library"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reply-To Email
                    </label>
                    <input
                      type="email"
                      name="replyToEmail"
                      value={config.replyToEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="smtpSecure"
                      checked={config.smtpSecure}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-700">
                      Use SSL/TLS (port 465)
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings Tab */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="enableNotifications"
                    checked={config.enableNotifications}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="text-lg font-medium text-gray-900">
                    Enable Email Notifications
                  </label>
                </div>

                {config.enableNotifications && (
                  <div className="space-y-4 pl-7">
                    <h3 className="text-md font-medium text-gray-900 mb-4">Notification Types</h3>
                    
                    {[
                      { key: 'newPrompts', label: 'New Prompts', description: 'Notify users when new prompts are added' },
                      { key: 'promptRatings', label: 'Prompt Ratings', description: 'Notify users when their prompts receive ratings' },
                      { key: 'platformAnnouncements', label: 'Platform Announcements', description: 'Send platform updates and announcements' },
                      { key: 'userRegistration', label: 'User Registration', description: 'Send welcome emails to new users' },
                      { key: 'promptVerification', label: 'Prompt Verification', description: 'Notify users when their prompts are verified' }
                    ].map((notification) => (
                      <div key={notification.key} className="flex items-start space-x-3 p-4 rounded-lg border border-gray-200 hover:bg-gray-50">
                        <input
                          type="checkbox"
                          name={`notificationTypes.${notification.key}`}
                          checked={config.notificationTypes[notification.key as keyof typeof config.notificationTypes]}
                          onChange={handleInputChange}
                          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-900 cursor-pointer">
                            {notification.label}
                          </label>
                          <p className="text-xs text-gray-500 mt-1">
                            {notification.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Test Email Tab */}
            {activeTab === 'test' && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Email Address
                  </label>
                  <input
                    type="email"
                    name="testEmail"
                    value={config.testEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-2 text-sm text-gray-500">
                    Enter an email address to test your SMTP configuration
                  </p>
                </div>

                {config.lastTestSent && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-700">
                      <strong>Last test sent:</strong> {new Date(config.lastTestSent).toLocaleString()}
                    </p>
                  </div>
                )}

                <button
                  onClick={handleTestEmail}
                  disabled={testing || !config.testEmail}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {testing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <span>Send Test Email</span>
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Scheduled Announcements Tab */}
            {activeTab === 'announcements' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">Scheduled Announcements</h3>
                  <button
                    onClick={() => {
                      resetAnnouncementForm();
                      setEditingAnnouncement(null);
                      setShowAnnouncementForm(true);
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    <span>New Announcement</span>
                  </button>
                </div>

                {/* Job Status Section */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">Background Job Status</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        The background job automatically sends scheduled announcements at their designated times.
                      </p>
                      {jobStatus && (
                        <div className="flex items-center space-x-4 mt-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            jobStatus.isRunning
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {jobStatus.isRunning ? '🟢 Running' : '🔴 Stopped'}
                          </span>
                          <span className="text-sm text-gray-500">
                            Check interval: {jobStatus.checkInterval / 1000}s
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      {jobStatus?.isRunning ? (
                        <button
                          onClick={() => handleJobAction('stop')}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                        >
                          Stop Job
                        </button>
                      ) : (
                        <button
                          onClick={() => handleJobAction('start')}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Start Job
                        </button>
                      )}
                      <button
                        onClick={() => handleJobAction('trigger')}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        Check Now
                      </button>
                    </div>
                  </div>
                </div>

                {/* Announcement Form Modal */}
                {showAnnouncementForm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="text-lg font-medium">
                          {editingAnnouncement ? 'Edit Announcement' : 'Create New Announcement'}
                        </h4>
                        <button
                          onClick={() => {
                            setShowAnnouncementForm(false);
                            setEditingAnnouncement(null);
                          }}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>

                      <form onSubmit={handleAnnouncementSubmit} className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                          <input
                            type="text"
                            value={announcementForm.title}
                            onChange={(e) => setAnnouncementForm(prev => ({ ...prev, title: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Email Subject</label>
                          <input
                            type="text"
                            value={announcementForm.subject}
                            onChange={(e) => setAnnouncementForm(prev => ({ ...prev, subject: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">HTML Content</label>
                          <textarea
                            value={announcementForm.htmlContent}
                            onChange={(e) => setAnnouncementForm(prev => ({ ...prev, htmlContent: e.target.value }))}
                            rows={6}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="<h2>Announcement Title</h2><p>Your announcement content here...</p>"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Text Content</label>
                          <textarea
                            value={announcementForm.textContent}
                            onChange={(e) => setAnnouncementForm(prev => ({ ...prev, textContent: e.target.value }))}
                            rows={4}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Plain text version of your announcement..."
                            required
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Scheduled Date</label>
                            <input
                              type="date"
                              value={announcementForm.scheduledDate}
                              onChange={(e) => setAnnouncementForm(prev => ({ ...prev, scheduledDate: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Scheduled Time</label>
                            <input
                              type="time"
                              value={announcementForm.scheduledTime}
                              onChange={(e) => setAnnouncementForm(prev => ({ ...prev, scheduledTime: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Recipients</label>
                          <select
                            value={announcementForm.recipientType}
                            onChange={(e) => setAnnouncementForm(prev => ({
                              ...prev,
                              recipientType: e.target.value as 'all_users' | 'specific_emails' | 'user_roles'
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="all_users">All Users</option>
                            <option value="specific_emails">Specific Email Addresses</option>
                            <option value="user_roles">Specific User Roles</option>
                          </select>
                        </div>

                        {announcementForm.recipientType === 'specific_emails' && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email Addresses</label>
                            <textarea
                              value={announcementForm.specificEmails}
                              onChange={(e) => setAnnouncementForm(prev => ({ ...prev, specificEmails: e.target.value }))}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="<EMAIL>, <EMAIL>, ..."
                            />
                            <p className="text-sm text-gray-500 mt-1">Separate multiple email addresses with commas</p>
                          </div>
                        )}

                        {announcementForm.recipientType === 'user_roles' && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">User Roles</label>
                            <div className="space-y-2">
                              {['admin', 'user', 'moderator'].map(role => (
                                <label key={role} className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={announcementForm.userRoles.includes(role)}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        setAnnouncementForm(prev => ({
                                          ...prev,
                                          userRoles: [...prev.userRoles, role]
                                        }));
                                      } else {
                                        setAnnouncementForm(prev => ({
                                          ...prev,
                                          userRoles: prev.userRoles.filter(r => r !== role)
                                        }));
                                      }
                                    }}
                                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                  <span className="text-sm text-gray-700 capitalize">{role}</span>
                                </label>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="flex justify-end space-x-3 pt-4">
                          <button
                            type="button"
                            onClick={() => {
                              setShowAnnouncementForm(false);
                              setEditingAnnouncement(null);
                            }}
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                          >
                            {editingAnnouncement ? 'Update' : 'Schedule'} Announcement
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                )}

                {/* Announcements List */}
                <div className="space-y-4">
                  {announcements.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p>No scheduled announcements yet</p>
                      <p className="text-sm">Create your first announcement to get started</p>
                    </div>
                  ) : (
                    announcements.map((announcement) => (
                      <div key={announcement._id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{announcement.title}</h4>
                            <p className="text-sm text-gray-600 mt-1">{announcement.subject}</p>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span>📅 {new Date(announcement.scheduledDate).toLocaleString()}</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                announcement.status === 'sent' ? 'bg-green-100 text-green-800' :
                                announcement.status === 'failed' ? 'bg-red-100 text-red-800' :
                                announcement.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {announcement.status}
                              </span>
                              {announcement.status === 'sent' && (
                                <span>✅ {announcement.successfulDeliveries}/{announcement.totalRecipients} delivered</span>
                              )}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            {announcement.status !== 'sent' && (
                              <>
                                <button
                                  onClick={() => handleEditAnnouncement(announcement)}
                                  className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  Edit
                                </button>
                                <button
                                  onClick={() => handleSendNow(announcement._id!)}
                                  className="text-green-600 hover:text-green-800 text-sm"
                                >
                                  Send Now
                                </button>
                              </>
                            )}
                            {announcement.status !== 'sent' && (
                              <button
                                onClick={() => handleDeleteAnnouncement(announcement._id!)}
                                className="text-red-600 hover:text-red-800 text-sm"
                              >
                                Delete
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <div className="bg-gray-50 px-6 py-4 flex justify-end">
            <button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Save Configuration</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
