'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { User, Settings, Shield, Bell, Eye, Globe, Smartphone, Save, Check, X, AlertCircle, Camera, Edit3 } from 'lucide-react';

interface UserProfile {
  _id: string;
  name: string;
  email: string;
  role: string;
  bio?: string;
  preferences: {
    emailNotifications: boolean;
    publicProfile: boolean;
    defaultPlatform: string;
  };
  createdAt: string;
  activity_log: Array<{
    action: string;
    timestamp: string;
  }>;
}

export default function SettingsPage() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [platforms, setPlatforms] = useState<string[]>(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'security'>('profile');

  const [formData, setFormData] = useState({
    name: '',
    bio: '',
    emailNotifications: true,
    publicProfile: false,
    defaultPlatform: 'ChatGPT',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin?callbackUrl=/settings');
      return;
    }

    loadUserProfile();
    loadPlatforms();
  }, [session, status, router]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/user/profile');
      const data = await response.json();
      
      if (response.ok) {
        setProfile(data);
        setFormData({
          name: data.name || '',
          bio: data.bio || '',
          emailNotifications: data.preferences?.emailNotifications ?? true,
          publicProfile: data.preferences?.publicProfile ?? false,
          defaultPlatform: data.preferences?.defaultPlatform || 'ChatGPT',
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
      } else {
        setError(data.message || 'Failed to load profile');
      }
    } catch (err) {
      setError('Failed to load profile');
      console.error('Profile error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPlatforms = async () => {
    try {
      const response = await fetch('/api/platforms');
      const data = await response.json();

      if (response.ok) {
        setPlatforms(data.platforms || ['ChatGPT', 'GitHub Copilot', 'Midjourney']);
      } else {
        console.error('Failed to load platforms:', data.message);
        // Fallback to default platforms
        setPlatforms(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
      }
    } catch (error) {
      console.error('Failed to load platforms:', error);
      // Fallback to default platforms
      setPlatforms(['ChatGPT', 'GitHub Copilot', 'Midjourney']);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const updateData = {
        name: formData.name.trim(),
        bio: formData.bio.trim(),
        preferences: {
          emailNotifications: formData.emailNotifications,
          publicProfile: formData.publicProfile,
          defaultPlatform: formData.defaultPlatform,
        }
      };

      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (response.ok) {
        setProfile(data);
        setSuccess('Profile updated successfully!');
        
        // Update session if name changed
        if (formData.name !== session?.user?.name) {
          await update({ name: formData.name });
        }
      } else {
        setError(data.message || 'Failed to update profile');
      }
    } catch (err) {
      setError('Failed to update profile');
      console.error('Update error:', err);
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    if (formData.newPassword !== formData.confirmPassword) {
      setError('New passwords do not match');
      setSaving(false);
      return;
    }

    if (formData.newPassword.length < 6) {
      setError('New password must be at least 6 characters long');
      setSaving(false);
      return;
    }

    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Password changed successfully!');
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        }));
      } else {
        setError(data.message || 'Failed to change password');
      }
    } catch (err) {
      setError('Failed to change password');
      console.error('Password change error:', err);
    } finally {
      setSaving(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <span className="text-muted-foreground">Loading settings...</span>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'preferences', name: 'Preferences', icon: Settings },
    { id: 'security', name: 'Security', icon: Shield },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="relative bg-gradient-primary overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm mb-6">
              <Settings className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-3">
              Account Settings
            </h1>
            <p className="text-white/90 text-lg max-w-2xl mx-auto">
              Manage your account preferences, security settings, and personalize your experience
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success/Error Messages */}
        {success && (
          <div className="mb-8 bg-success-light border border-success/20 rounded-xl p-4 animate-fade-in">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Check className="w-5 h-5 text-success" />
              </div>
              <div className="flex-1">
                <p className="text-success font-medium">{success}</p>
              </div>
              <button
                onClick={() => setSuccess(null)}
                className="flex-shrink-0 text-success hover:text-success/80 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-8 bg-destructive-light border border-destructive/20 rounded-xl p-4 animate-fade-in">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <AlertCircle className="w-5 h-5 text-destructive" />
              </div>
              <div className="flex-1">
                <p className="text-destructive font-medium">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="flex-shrink-0 text-destructive hover:text-destructive/80 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        <div className="card overflow-hidden shadow-lg">
          {/* Tab Navigation */}
          <div className="border-b border-border bg-surface/30">
            <nav className="flex space-x-1 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`relative flex items-center space-x-2 py-4 px-4 font-medium text-sm transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                    {activeTab === tab.id && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"></div>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-8">
            {activeTab === 'profile' && (
              <form onSubmit={handleProfileUpdate} className="space-y-8">
                <div>
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full bg-gradient-primary flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                        {session?.user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                      <button type="button" className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center shadow-md hover:bg-primary-hover transition-colors">
                        <Camera className="w-4 h-4" />
                      </button>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-1">Profile Information</h3>
                      <p className="text-muted-foreground">Update your personal details and preferences</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="flex items-center text-sm font-medium text-foreground">
                        <User className="w-4 h-4 mr-2 text-primary" />
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="input w-full"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="email" className="flex items-center text-sm font-medium text-foreground">
                        <Globe className="w-4 h-4 mr-2 text-primary" />
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={profile?.email || ''}
                        className="input w-full opacity-60 cursor-not-allowed"
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">Email cannot be changed</p>
                    </div>
                  </div>

                  <div className="mt-6 space-y-2">
                    <label htmlFor="bio" className="flex items-center text-sm font-medium text-foreground">
                      <Edit3 className="w-4 h-4 mr-2 text-primary" />
                      Bio
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      value={formData.bio}
                      onChange={handleInputChange}
                      rows={4}
                      className="input w-full resize-none"
                      placeholder="Tell us about yourself..."
                    />
                  </div>

                  <div className="flex justify-end pt-6 border-t border-border">
                    <button
                      type="submit"
                      disabled={saving}
                      className="btn btn-primary btn-lg min-w-[140px]"
                    >
                      {saving ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          <span>Saving...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <Save className="w-4 h-4" />
                          <span>Save Changes</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            )}

            {activeTab === 'preferences' && (
              <form onSubmit={handleProfileUpdate} className="space-y-8">
                <div>
                  <div className="flex items-center space-x-3 mb-8">
                    <div className="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center">
                      <Settings className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-1">Preferences</h3>
                      <p className="text-muted-foreground">Customize your experience and notification settings</p>
                    </div>
                  </div>

                  <div className="space-y-8">
                    <div className="space-y-3">
                      <label htmlFor="defaultPlatform" className="flex items-center text-sm font-medium text-foreground">
                        <Smartphone className="w-4 h-4 mr-2 text-primary" />
                        Default Platform
                      </label>
                      <select
                        id="defaultPlatform"
                        name="defaultPlatform"
                        value={formData.defaultPlatform}
                        onChange={handleInputChange}
                        className="input w-full"
                      >
                        {platforms.map(platform => (
                          <option key={platform} value={platform}>{platform}</option>
                        ))}
                      </select>
                      <p className="text-xs text-muted-foreground">This will be pre-selected when creating new prompts</p>
                    </div>

                    <div className="space-y-6">
                      <h4 className="text-lg font-semibold text-foreground flex items-center">
                        <Bell className="w-5 h-5 mr-2 text-primary" />
                        Notification Settings
                      </h4>

                      <div className="space-y-4">
                        <div className="flex items-start space-x-3 p-4 rounded-lg border border-border hover:bg-surface/50 transition-colors">
                          <input
                            id="emailNotifications"
                            name="emailNotifications"
                            type="checkbox"
                            checked={formData.emailNotifications}
                            onChange={handleInputChange}
                            className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border rounded"
                          />
                          <div className="flex-1">
                            <label htmlFor="emailNotifications" className="block text-sm font-medium text-foreground cursor-pointer">
                              Email notifications
                            </label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Receive updates about new prompts, ratings, and platform announcements
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start space-x-3 p-4 rounded-lg border border-border hover:bg-surface/50 transition-colors">
                          <input
                            id="publicProfile"
                            name="publicProfile"
                            type="checkbox"
                            checked={formData.publicProfile}
                            onChange={handleInputChange}
                            className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border rounded"
                          />
                          <div className="flex-1">
                            <label htmlFor="publicProfile" className="block text-sm font-medium text-foreground cursor-pointer">
                              <div className="flex items-center space-x-2">
                                <span>Public profile</span>
                                <Eye className="w-4 h-4 text-muted-foreground" />
                              </div>
                            </label>
                            <p className="text-xs text-muted-foreground mt-1">
                              Show your name and bio on prompts you create. Others can see your contributions.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end pt-6 border-t border-border">
                    <button
                      type="submit"
                      disabled={saving}
                      className="btn btn-primary btn-lg min-w-[160px]"
                    >
                      {saving ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          <span>Saving...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <Save className="w-4 h-4" />
                          <span>Save Preferences</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            )}

            {activeTab === 'security' && (
              <div className="space-y-8">
                <div>
                  <div className="flex items-center space-x-3 mb-8">
                    <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                      <Shield className="w-6 h-6 text-destructive" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-1">Account Security</h3>
                      <p className="text-muted-foreground">Manage your account security and access permissions</p>
                    </div>
                  </div>

                  <div className="bg-surface/50 rounded-xl p-6 mb-8 border border-border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <User className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-foreground">Account Role</h4>
                          <p className="text-sm text-muted-foreground">Your current account permissions</p>
                        </div>
                      </div>
                      <span className={`badge ${
                        profile?.role === 'admin'
                          ? 'bg-destructive text-destructive-foreground'
                          : profile?.role === 'moderator'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-secondary text-secondary-foreground'
                      }`}>
                        {profile?.role}
                      </span>
                    </div>
                  </div>

                  <form onSubmit={handlePasswordChange} className="space-y-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <Shield className="w-5 h-5 text-primary" />
                      <h4 className="text-lg font-semibold text-foreground">Change Password</h4>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="currentPassword" className="text-sm font-medium text-foreground">
                          Current Password
                        </label>
                        <input
                          type="password"
                          id="currentPassword"
                          name="currentPassword"
                          value={formData.currentPassword}
                          onChange={handleInputChange}
                          className="input w-full"
                          placeholder="Enter your current password"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="newPassword" className="text-sm font-medium text-foreground">
                          New Password
                        </label>
                        <input
                          type="password"
                          id="newPassword"
                          name="newPassword"
                          value={formData.newPassword}
                          onChange={handleInputChange}
                          className="input w-full"
                          placeholder="Enter your new password"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="confirmPassword" className="text-sm font-medium text-foreground">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          id="confirmPassword"
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          className="input w-full"
                          placeholder="Confirm your new password"
                          required
                        />
                      </div>
                    </div>

                    <div className="flex justify-end pt-6 border-t border-border">
                      <button
                        type="submit"
                        disabled={saving}
                        className="btn btn-destructive btn-lg min-w-[160px]"
                      >
                        {saving ? (
                          <div className="flex items-center space-x-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                            <span>Changing...</span>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <Shield className="w-4 h-4" />
                            <span>Change Password</span>
                          </div>
                        )}
                      </button>
                    </div>
                  </form>
                </div>

                {/* Account Stats */}
                <div className="border-t border-border pt-8">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-8 h-8 rounded-full bg-accent/10 flex items-center justify-center">
                      <Settings className="w-4 h-4 text-accent" />
                    </div>
                    <h4 className="text-lg font-semibold text-foreground">Account Statistics</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="card p-6 text-center hover:shadow-md transition-shadow">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
                        <Settings className="w-6 h-6 text-primary" />
                      </div>
                      <div className="text-3xl font-bold text-primary mb-1">
                        {profile?.activity_log?.length || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Actions</div>
                    </div>
                    <div className="card p-6 text-center hover:shadow-md transition-shadow">
                      <div className="w-12 h-12 rounded-full bg-success/10 flex items-center justify-center mx-auto mb-3">
                        <User className="w-6 h-6 text-success" />
                      </div>
                      <div className="text-3xl font-bold text-success mb-1">
                        {profile?.createdAt ? new Date(profile.createdAt).toLocaleDateString() : 'N/A'}
                      </div>
                      <div className="text-sm text-muted-foreground">Member Since</div>
                    </div>
                    <div className="card p-6 text-center hover:shadow-md transition-shadow">
                      <div className="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center mx-auto mb-3">
                        <Shield className="w-6 h-6 text-accent" />
                      </div>
                      <div className="text-3xl font-bold text-accent mb-1">
                        {profile?.role === 'admin' ? 'Full' : profile?.role === 'moderator' ? 'Limited' : 'Basic'}
                      </div>
                      <div className="text-sm text-muted-foreground">Access Level</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
