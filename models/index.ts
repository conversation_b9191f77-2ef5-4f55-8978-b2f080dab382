// Import all models to ensure they are registered with Mongoose
// This helps prevent "<PERSON><PERSON><PERSON> hasn't been registered" errors

import User from './User';
import Category from './Category';
import Prompt from './Prompt';
import Platform from './Platform';
import Analytics from './Analytics';
import Favorite from './Favorite';

// Export all models for convenience
export {
  User,
  Category,
  Prompt,
  Platform,
  Analytics,
  Favorite
};

// Default export with all models
export default {
  User,
  Category,
  Prompt,
  Platform,
  Analytics,
  Favorite
};
