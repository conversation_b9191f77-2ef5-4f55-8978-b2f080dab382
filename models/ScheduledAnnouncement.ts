import mongoose, { Schema, Document } from 'mongoose';

export interface IScheduledAnnouncement extends Document {
  // Announcement Content
  title: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  
  // Scheduling
  scheduledDate: Date;
  timezone: string;
  
  // Recipients
  recipientType: 'all_users' | 'specific_emails' | 'user_roles';
  specificEmails: string[];
  userRoles: string[];
  
  // Status
  status: 'draft' | 'scheduled' | 'sent' | 'failed' | 'cancelled';
  sentAt?: Date;
  failureReason?: string;
  
  // Delivery Stats
  totalRecipients: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  
  // Metadata
  createdBy: string; // User ID who created the announcement
  lastModifiedBy: string; // User ID who last modified
  createdAt: Date;
  updatedAt: Date;
}

const ScheduledAnnouncementSchema = new Schema<IScheduledAnnouncement>({
  // Announcement Content
  title: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 200
  },
  subject: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 300
  },
  htmlContent: { 
    type: String, 
    required: true
  },
  textContent: { 
    type: String, 
    required: true
  },
  
  // Scheduling
  scheduledDate: { 
    type: Date, 
    required: true,
    index: true
  },
  timezone: { 
    type: String, 
    required: true,
    default: 'UTC'
  },
  
  // Recipients
  recipientType: { 
    type: String, 
    enum: ['all_users', 'specific_emails', 'user_roles'],
    required: true,
    default: 'all_users'
  },
  specificEmails: [{ 
    type: String,
    validate: {
      validator: function(email: string) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format'
    }
  }],
  userRoles: [{ 
    type: String,
    enum: ['admin', 'user', 'moderator']
  }],
  
  // Status
  status: { 
    type: String, 
    enum: ['draft', 'scheduled', 'sent', 'failed', 'cancelled'],
    required: true,
    default: 'draft',
    index: true
  },
  sentAt: { 
    type: Date 
  },
  failureReason: { 
    type: String 
  },
  
  // Delivery Stats
  totalRecipients: { 
    type: Number, 
    default: 0 
  },
  successfulDeliveries: { 
    type: Number, 
    default: 0 
  },
  failedDeliveries: { 
    type: Number, 
    default: 0 
  },
  
  // Metadata
  createdBy: { 
    type: String, 
    required: true 
  },
  lastModifiedBy: { 
    type: String, 
    required: true 
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient querying
ScheduledAnnouncementSchema.index({ status: 1, scheduledDate: 1 });
ScheduledAnnouncementSchema.index({ createdBy: 1 });
ScheduledAnnouncementSchema.index({ scheduledDate: 1, status: 1 });

// Virtual for checking if announcement is due
ScheduledAnnouncementSchema.virtual('isDue').get(function() {
  return this.status === 'scheduled' && new Date() >= this.scheduledDate;
});

// Virtual for delivery success rate
ScheduledAnnouncementSchema.virtual('deliverySuccessRate').get(function() {
  if (this.totalRecipients === 0) return 0;
  return (this.successfulDeliveries / this.totalRecipients) * 100;
});

// Pre-save middleware to validate scheduling
ScheduledAnnouncementSchema.pre('save', function(next) {
  // Ensure scheduled date is in the future for new announcements
  if (this.isNew && this.status === 'scheduled' && this.scheduledDate <= new Date()) {
    return next(new Error('Scheduled date must be in the future'));
  }
  
  // Validate recipients based on type
  if (this.recipientType === 'specific_emails' && this.specificEmails.length === 0) {
    return next(new Error('At least one email address is required for specific_emails recipient type'));
  }
  
  if (this.recipientType === 'user_roles' && this.userRoles.length === 0) {
    return next(new Error('At least one user role is required for user_roles recipient type'));
  }
  
  next();
});

export default mongoose.models.ScheduledAnnouncement || 
  mongoose.model<IScheduledAnnouncement>('ScheduledAnnouncement', ScheduledAnnouncementSchema);
