import mongoose, { Schema, Document } from 'mongoose';

export interface IEmailConfig extends Document {
  // SMTP Configuration
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean; // true for 465, false for other ports
  smtpUser: string;
  smtpPassword: string;
  
  // Email Settings
  fromEmail: string;
  fromName: string;
  replyToEmail?: string;
  
  // Notification Settings
  enableNotifications: boolean;
  notificationTypes: {
    newPrompts: boolean;
    promptRatings: boolean;
    platformAnnouncements: boolean;
    userRegistration: boolean;
    promptVerification: boolean;
  };
  
  // Email Templates
  templates: {
    newPrompt: {
      subject: string;
      htmlTemplate: string;
      textTemplate: string;
    };
    promptRating: {
      subject: string;
      htmlTemplate: string;
      textTemplate: string;
    };
    announcement: {
      subject: string;
      htmlTemplate: string;
      textTemplate: string;
    };
    welcome: {
      subject: string;
      htmlTemplate: string;
      textTemplate: string;
    };
    promptVerified: {
      subject: string;
      htmlTemplate: string;
      textTemplate: string;
    };
  };
  
  // Test Settings
  testEmail?: string;
  lastTestSent?: Date;
  
  // Metadata
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastModifiedBy: string; // User ID who last modified
}

const EmailConfigSchema = new Schema<IEmailConfig>({
  // SMTP Configuration
  smtpHost: { type: String, required: true, default: '' },
  smtpPort: { type: Number, required: true, default: 587 },
  smtpSecure: { type: Boolean, default: false },
  smtpUser: { type: String, required: true, default: '' },
  smtpPassword: { type: String, required: true, default: '' },
  
  // Email Settings
  fromEmail: { type: String, required: true, default: '' },
  fromName: { type: String, required: true, default: 'AI Prompt Library' },
  replyToEmail: { type: String, default: '' },
  
  // Notification Settings
  enableNotifications: { type: Boolean, default: false },
  notificationTypes: {
    newPrompts: { type: Boolean, default: true },
    promptRatings: { type: Boolean, default: true },
    platformAnnouncements: { type: Boolean, default: true },
    userRegistration: { type: Boolean, default: true },
    promptVerification: { type: Boolean, default: true },
  },
  
  // Email Templates
  templates: {
    newPrompt: {
      subject: { type: String, default: 'New Prompt Added: {{promptTitle}}' },
      htmlTemplate: { 
        type: String, 
        default: `
          <h2>New Prompt Added</h2>
          <p>A new prompt has been added to the AI Prompt Library:</p>
          <h3>{{promptTitle}}</h3>
          <p>{{promptDescription}}</p>
          <p><strong>Platform:</strong> {{promptPlatform}}</p>
          <p><strong>Category:</strong> {{promptCategory}}</p>
          <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Prompt</a></p>
        `
      },
      textTemplate: { 
        type: String, 
        default: `New Prompt Added: {{promptTitle}}\n\n{{promptDescription}}\n\nPlatform: {{promptPlatform}}\nCategory: {{promptCategory}}\n\nView at: {{promptUrl}}`
      }
    },
    promptRating: {
      subject: { type: String, default: 'Your prompt received a new rating' },
      htmlTemplate: { 
        type: String, 
        default: `
          <h2>Your Prompt Received a Rating</h2>
          <p>Your prompt "{{promptTitle}}" has received a new {{ratingType}} rating!</p>
          <p><strong>Current Rating:</strong> {{currentRating}}/5 ({{totalRatings}} ratings)</p>
          <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Prompt</a></p>
        `
      },
      textTemplate: { 
        type: String, 
        default: `Your prompt "{{promptTitle}}" received a new {{ratingType}} rating!\n\nCurrent Rating: {{currentRating}}/5 ({{totalRatings}} ratings)\n\nView at: {{promptUrl}}`
      }
    },
    announcement: {
      subject: { type: String, default: '{{announcementTitle}} - AI Prompt Library' },
      htmlTemplate: { 
        type: String, 
        default: `
          <h2>{{announcementTitle}}</h2>
          <div>{{announcementContent}}</div>
          <p><a href="{{platformUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Visit Platform</a></p>
        `
      },
      textTemplate: { 
        type: String, 
        default: `{{announcementTitle}}\n\n{{announcementContent}}\n\nVisit: {{platformUrl}}`
      }
    },
    welcome: {
      subject: { type: String, default: 'Welcome to AI Prompt Library!' },
      htmlTemplate: { 
        type: String, 
        default: `
          <h2>Welcome to AI Prompt Library, {{userName}}!</h2>
          <p>Thank you for joining our community of AI prompt enthusiasts.</p>
          <p>You can now:</p>
          <ul>
            <li>Browse and discover high-quality AI prompts</li>
            <li>Create and share your own prompts</li>
            <li>Rate and review prompts from the community</li>
            <li>Organize prompts by categories and platforms</li>
          </ul>
          <p><a href="{{platformUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Start Exploring</a></p>
        `
      },
      textTemplate: { 
        type: String, 
        default: `Welcome to AI Prompt Library, {{userName}}!\n\nThank you for joining our community. You can now browse prompts, create your own, and engage with the community.\n\nStart exploring: {{platformUrl}}`
      }
    },
    promptVerified: {
      subject: { type: String, default: 'Your prompt has been verified!' },
      htmlTemplate: { 
        type: String, 
        default: `
          <h2>Prompt Verified!</h2>
          <p>Congratulations! Your prompt "{{promptTitle}}" has been verified by our moderation team.</p>
          <p>Verified prompts receive higher visibility and are marked with a verification badge.</p>
          <p><a href="{{promptUrl}}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Your Verified Prompt</a></p>
        `
      },
      textTemplate: { 
        type: String, 
        default: `Your prompt "{{promptTitle}}" has been verified!\n\nVerified prompts receive higher visibility and are marked with a verification badge.\n\nView at: {{promptUrl}}`
      }
    }
  },
  
  // Test Settings
  testEmail: { type: String, default: '' },
  lastTestSent: { type: Date },
  
  // Metadata
  isActive: { type: Boolean, default: false },
  lastModifiedBy: { type: String, required: true }
}, { timestamps: true });

// Ensure only one active configuration exists
EmailConfigSchema.pre('save', async function(next) {
  if (this.isActive) {
    // Deactivate all other configurations
    await mongoose.model('EmailConfig').updateMany(
      { _id: { $ne: this._id } },
      { isActive: false }
    );
  }
  next();
});

export default mongoose.models.EmailConfig || mongoose.model<IEmailConfig>('EmailConfig', EmailConfigSchema);
